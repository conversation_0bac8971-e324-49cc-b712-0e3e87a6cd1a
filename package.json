{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "next": "15.4.6", "openai": "^5.12.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^24.2.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "eslint": "^9.33.0", "eslint-config-next": "15.4.6", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.9.2"}}
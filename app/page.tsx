"use client";

import {useState} from "react";
import {<PERSON><PERSON>} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {ScrollArea} from "@/components/ui/scroll-area";
import {Card, CardContent} from "@/components/ui/card";
import {Dropzone, DropzoneContent, DropzoneEmptyState} from "@/components/ui/shadcn-io/dropzone";
import {ImageIcon, X} from "lucide-react";

interface Message {
    id: number;
    text: string;
    sender: "user" | "bot";
    image?: string; // base64 encoded image
    imageFile?: File; // for display purposes
}

export default function Home() {
    const [messages, setMessages] = useState<Message[]>([
        {id: 1, text: "Hello! How can I help you today?", sender: "bot"},
    ]);
    const [inputText, setInputText] = useState("");
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const handleSend = async () => {
        if (!inputText.trim() && !selectedImage) return;

        // Convert image to base64 if present
        let imageBase64 = "";
        if (selectedImage) {
            const reader = new FileReader();
            imageBase64 = await new Promise<string>((resolve) => {
                reader.onload = () => {
                    const result = reader.result as string;
                    // Remove data URL prefix to get just the base64 string
                    resolve(result.split(',')[1]);
                };
                reader.readAsDataURL(selectedImage);
            });
        }

        const userMessage: Message = {
            id: Date.now(),
            text: inputText || (selectedImage ? "Shared an image" : ""),
            sender: "user",
            image: imageBase64 || undefined,
            imageFile: selectedImage || undefined,
        };

        setMessages((prev) => [...prev, userMessage]);
        setInputText("");
        setSelectedImage(null);

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: inputText || "What do you see in this image?",
                    image: imageBase64 || undefined
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to get response');
            }

            const data = await response.json();

            setMessages((prev) => [
                ...prev,
                {
                    id: Date.now() + 1,
                    text: data.response,
                    sender: "bot",
                },
            ]);
        } catch (error) {
            console.error('Error sending message:', error);
            setMessages((prev) => [
                ...prev,
                {
                    id: Date.now() + 2,
                    text: "Sorry, I encountered an error. Please try again.",
                    sender: "bot",
                },
            ]);
        }
    };

    const handleImageUpload = (files: File[]) => {
        if (files.length > 0) {
            setSelectedImage(files[0]);
        }
    };

    const removeSelectedImage = () => {
        setSelectedImage(null);
    };



    return (
        <div className="flex flex-col h-screen">
            <header className="border-b px-6 py-4">
                <h1 className="text-2xl font-bold tracking-tight">Chat Interface</h1>
            </header>

            <ScrollArea className="flex-1 p-4">
                <div className="flex flex-col gap-4 max-w-3xl mx-auto">
                    {messages.map((message) => (
                        <div
                            key={message.id}
                            className={`flex ${
                                message.sender === "user" ? "justify-end" : "justify-start"
                            }`}
                        >
                            <Card
                                className={`max-w-[70%] ${
                                    message.sender === "user" ? "bg-primary" : "bg-muted"
                                }`}
                            >
                                <CardContent
                                    className={`p-3 ${
                                        message.sender === "user"
                                            ? "text-primary-foreground"
                                            : "text-muted-foreground"
                                    }`}
                                >
                                    {message.image && (
                                        <div className="mb-2">
                                            <img
                                                src={message.imageFile ? URL.createObjectURL(message.imageFile) : `data:image/jpeg;base64,${message.image}`}
                                                alt="Uploaded image"
                                                className="max-w-full h-auto rounded-md max-h-64 object-contain"
                                            />
                                        </div>
                                    )}
                                    {message.text}
                                </CardContent>
                            </Card>
                        </div>
                    ))}
                </div>
            </ScrollArea>

            <footer className="border-t p-4">
                <div className="max-w-3xl mx-auto">
                    {selectedImage && (
                        <div className="mb-4 p-3 border rounded-lg bg-muted/50">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium">Selected Image:</span>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={removeSelectedImage}
                                    className="h-6 w-6 p-0"
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                            <img
                                src={URL.createObjectURL(selectedImage)}
                                alt="Selected image"
                                className="max-w-full h-auto rounded-md max-h-32 object-contain"
                            />
                        </div>
                    )}
                    <div className="flex gap-2">
                        <Input
                            value={inputText}
                            onChange={(e) => setInputText(e.target.value)}
                            onKeyPress={(e) => e.key === "Enter" && handleSend()}
                            placeholder="Type a message..."
                            className="flex-1"
                        />
                        <Dropzone
                            onDrop={handleImageUpload}
                            accept={{ 'image/*': [] }}
                            maxFiles={1}
                            className="h-9 w-9 p-0"
                        >
                            <DropzoneEmptyState className="p-0">
                                <ImageIcon className="h-4 w-4" />
                            </DropzoneEmptyState>
                            <DropzoneContent className="p-0">
                                <ImageIcon className="h-4 w-4" />
                            </DropzoneContent>
                        </Dropzone>
                        <Button onClick={handleSend}>Send</Button>
                    </div>
                </div>
            </footer>
        </div>
    );
}

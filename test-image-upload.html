<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Upload</title>
</head>
<body>
    <h1>Test Image Upload to Chat API</h1>
    
    <form id="testForm">
        <div>
            <label for="message">Message:</label>
            <input type="text" id="message" value="What do you see in this image?" />
        </div>
        <div>
            <label for="image">Image:</label>
            <input type="file" id="image" accept="image/*" />
        </div>
        <button type="submit">Send Test</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const messageInput = document.getElementById('message');
            const imageInput = document.getElementById('image');
            const resultDiv = document.getElementById('result');
            
            if (!imageInput.files[0]) {
                alert('Please select an image');
                return;
            }
            
            try {
                // Convert image to base64
                const file = imageInput.files[0];
                const reader = new FileReader();
                
                reader.onload = async () => {
                    const base64 = reader.result.split(',')[1]; // Remove data URL prefix
                    
                    const response = await fetch('http://localhost:3001/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: messageInput.value,
                            image: base64
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        resultDiv.innerHTML = `<h3>Success!</h3><p>${data.response}</p>`;
                    } else {
                        resultDiv.innerHTML = `<h3>Error!</h3><p>${data.error}</p>`;
                    }
                };
                
                reader.readAsDataURL(file);
                
            } catch (error) {
                resultDiv.innerHTML = `<h3>Error!</h3><p>${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
